{% extends "partials/base.html" %}
{% block content %}
<h2>Expense Management</h2>

<!-- Add Expense Button - Show to users who can add expenses -->
{% if can_add_expenses %}
<a
    class="btn btn-primary btn-sm my-2" hx-get="{% url 'store:add_expense_form' %}" hx-target="#modal-body" hx-trigger="click" data-toggle="modal" data-target="#expenseModal">
    Add Expense
</a>

<!-- Add Expense Category Button - Show to users who can add expenses -->
<a
    class="btn btn-secondary btn-sm my-2" hx-get="{% url 'store:add_expense_category_form' %}" hx-target="#modal-body"
    hx-trigger="click" data-toggle="modal" data-target="#expenseModal">
    Add Expense Category
</a>
{% endif %}

<a href="{% url "store:monthly_sales_deduction" %}" class="btn btn-info btn-sm my-2"> Generate Expense Summary </a>

<a href="{% url 'store:generate_monthly_report' %}?month={% now 'Y-m' %}" class="btn btn-sm btn-success btn-sm my-2"> Generate Expense Report </a>

<!-- Expense List Table -->
<div id="expense-list">{% include 'partials/_expense_list.html' %}</div>

<!-- Optionally, you can also include the current list of expense categories -->
<div id="expense-category">
    {% include 'partials/_expense_category_list.html' with categories=expense_categories %}
</div>

<!-- Bootstrap Modal -->
<div class="modal fade" id="expenseModal" tabindex="-1" aria-labelledby="expenseModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="expenseModalLabel">Add Expense / Category</h5>
                <button type="button" class="close"  data-dismiss="modal" aria-label="Close">x</button>
            </div>
            <div class="modal-body" id="modal-body">
                <!-- Form content will be loaded here via HTMX -->
            </div>
        </div>
    </div>
</div>

{% endblock content %}
